from fastapi import APIRouter, Request, HTTPException
from pydantic import BaseModel
from google.cloud import firestore
from fastapi.responses import RedirectResponse, HTMLResponse
from firebase.config import get_db
import logging
from urllib.parse import urlparse, urlencode, parse_qs, urlunparse
router = APIRouter()
db = get_db()
logger = logging.getLogger(__name__)

def build_admesh_link(ad_id: str, product_id: str = None, redirect_url: str = None,
                     rec_id: str = "", session_id: str = "",
                     agent_id: str = "", user_id: str = "",
                     is_test: bool = False) -> str:
    """
    Build a complete AdMesh tracking link with all required parameters.

    The function will use the ad_id to get the offer, extract product_id from the offer,
    and get the redirect_url from the product if they are not provided.

    Args:
        ad_id: The ad ID or offer ID (required)
        product_id: The product ID (optional, will be fetched from offer if not provided)
        redirect_url: The URL to redirect to after tracking (optional, will be fetched from product if not provided)
        rec_id: The recommendation ID
        session_id: The session ID
        agent_id: The agent ID
        user_id: The user ID
        is_test: Whether this is a test link

    Returns:
        A fully constructed AdMesh link with all parameters properly encoded as UTM parameters
    """
    import os
    from api.routes.openrouter import get_site_url

    # Base URL for the AdMesh redirect
    base_url = f"{get_site_url()}/click/r/{ad_id}"

    # Build query parameters using UTM format only
    params = {}
    if product_id:
        params["utm_product"] = product_id
    if redirect_url:
        params["utm_redirect"] = redirect_url

    # Add optional parameters if they exist
    if rec_id:
        params["utm_rec"] = rec_id
    if session_id:
        params["utm_session"] = session_id
    if agent_id:
        params["utm_agent"] = agent_id
    if user_id:
        params["utm_user"] = user_id

    # Add test=true if in development environment or if explicitly requested
    env = os.getenv("ENV", "development")
    if is_test or env == "development":
        params["test"] = "true"

    # Encode parameters and build final URL
    query_string = urlencode(params)
    return f"{base_url}?{query_string}"

class ClickEvent(BaseModel):
    product_id: str | None = None
    offer_id: str | None = None
    agent_id: str
    user_id: str | None = None
    session_id: str | None = None
    query: str | None = None  # ✅ NEW FIELD
    is_test: bool = False  # Flag to mark test clicks

@router.get("/r/{ad_id}", response_class=HTMLResponse)
async def redirect_from_ad_id(ad_id: str, request: Request):
    try:
        is_test = request.query_params.get('test', '').lower() == 'true'

        # Get parameters from query string - support both UTM format and legacy format
        session_id = request.query_params.get("utm_session") or request.query_params.get("session_id")
        agent_id = request.query_params.get("utm_agent") or request.query_params.get("agent_id")
        user_id = request.query_params.get("utm_user") or request.query_params.get("user_id")
        recommendation_id = request.query_params.get("utm_rec") or request.query_params.get("rec_id")
        redirect_url = request.query_params.get("utm_redirect") or request.query_params.get("redirect_url")
        product_id = request.query_params.get("utm_product") or request.query_params.get("product_id")
        offer_id = request.query_params.get("offer_id", ad_id)

        # Debug logging
        logger.info(f"🔍 Debug info - ad_id: {ad_id}, offer_id: {offer_id}, product_id: {product_id}, redirect_url: {redirect_url}")
        logger.info(f"🔍 All query params: {dict(request.query_params)}")

        # Get offer details to extract product_id if not provided in query params
        if not product_id:
            logger.info(f"No product_id provided in query params, fetching from offer {offer_id}")
            try:
                offer_ref = db.collection("offers").document(offer_id)
                offer_doc = offer_ref.get()

                if offer_doc.exists:
                    offer_data = offer_doc.to_dict()
                    product_id = offer_data.get("product_id")
                    logger.info(f"Found product_id in offer: {product_id}")

                    if not product_id:
                        logger.error(f"Offer {offer_id} does not have a product_id")
                        raise HTTPException(400, detail=f"Offer {offer_id} does not have a product_id")
                else:
                    logger.error(f"Offer {offer_id} not found")
                    raise HTTPException(404, detail=f"Offer {offer_id} not found")
            except Exception as e:
                logger.exception(f"Error fetching offer {offer_id}: {str(e)}")
                raise HTTPException(500, detail=f"Error fetching offer: {str(e)}")
        else:
            logger.info(f"✅ product_id already provided: {product_id}, skipping offer lookup")

        # Get product details to extract redirect_url if not provided in query params
        if not redirect_url:
            logger.info(f"No redirect_url provided in query params, fetching from product {product_id}")
            try:
                product_doc = db.collection("products").document(product_id).get()
                if product_doc.exists:
                    product_data = product_doc.to_dict()
                    redirect_url = product_data.get("url")
                    logger.info(f"Found redirect_url in product: {redirect_url}")
                else:
                    logger.error(f"Product {product_id} not found")
                    raise HTTPException(404, detail=f"Product {product_id} not found")
            except Exception as e:
                logger.exception(f"Error fetching product {product_id}: {str(e)}")
                raise HTTPException(500, detail=f"Error fetching product: {str(e)}")

        # Final validation of redirect_url
        if not redirect_url:
            raise HTTPException(400, detail="Missing redirect_url and could not find URL in product")

        # Ensure redirect_url is a proper URL with protocol
        if not redirect_url.startswith(('http://', 'https://')):
            redirect_url = f"https://{redirect_url}"
            logger.info(f"Added https:// protocol to redirect_url: {redirect_url}")

        click_ref = db.collection("clicks").document()
        click_id = f"test_{click_ref.id}" if is_test else click_ref.id

        click_data = {
            "click_id": click_id,
            "ad_id": ad_id,
            "product_id": product_id,
            "offer_id": offer_id,
            "session_id": session_id,
            "agent_id": agent_id,
            "user_id": user_id,
            "recommendation_id": recommendation_id,
            "referrer": request.headers.get("referer"),
            "ip_address": request.client.host,
            "user_agent": request.headers.get("user-agent"),
            "timestamp": firestore.SERVER_TIMESTAMP,
            "is_test": is_test,
            "click_type": "test" if is_test else "initial",
            "source": "test" if is_test else "production",
            "is_unclaimed_click": False
        }

        db.collection("clicks").document(click_id).set(click_data)

        # ✅ Try updating offer clicks
        offer_ref = db.collection("offers").document(ad_id)
        offer_doc = offer_ref.get()

        if offer_doc.exists:
            if is_test:
                offer_ref.update({
                    "click_count.test": firestore.Increment(1),
                    "click_count.total": firestore.Increment(1),
                    "offer_views.test": firestore.Increment(1),
                    "offer_views.total": firestore.Increment(1)
                })
            else:
                offer_ref.update({
                    "click_count.production": firestore.Increment(1),
                    "click_count.total": firestore.Increment(1),
                    "offer_views.production": firestore.Increment(1),
                    "offer_views.total": firestore.Increment(1)
                })
        else:
            logger.info(f"Offer {ad_id} not found. Incrementing only product click count.")
            # ✅ Only update product clicks when offer is missing
            if not is_test and product_id:
                db.collection("products").document(product_id).update({
                    "clicks": firestore.Increment(1),
                    "last_clicked_at": firestore.SERVER_TIMESTAMP

                })
                db.collection("clicks").document(click_id).update({
                    "is_unclaimed_click": True
                 })

        # Build final redirect URL with UTM parameters
        parsed = urlparse(redirect_url)
        params = parse_qs(parsed.query)
        params["utm_click_id"] = [click_id]
        params["utm_source"] = ["admesh"]

        # Add product slug as utm_target if available
        if product_id:
            # Get product details to extract slug
            try:
                product_doc = db.collection("products").document(product_id).get()
                if product_doc.exists:
                    product_data = product_doc.to_dict()
                    product_slug = product_data.get("slug")
                    if product_slug:
                        params["utm_target"] = [product_slug]
                    else:
                        # Fallback to product_id if slug is not available
                        params["utm_target"] = [product_id]
            except Exception as e:
                logger.warning(f"Error getting product slug: {str(e)}")
                # Fallback to product_id
                params["utm_target"] = [product_id]

        if is_test:
            params["test"] = ["true"]
        final_query = urlencode(params, doseq=True)
        logger.info(f"Final query: {final_query}")
        final_redirect = urlunparse((parsed.scheme, parsed.netloc, parsed.path, '', final_query, ''))
        logger.info(f"Final redirect URL: {final_redirect}")

        if is_test:
            return RedirectResponse(url=final_redirect)

        # HTML Page for production
        domain = parsed.netloc
        html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta http-equiv="refresh" content="3;url={final_redirect}" />
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Redirecting to {domain}</title>
            <link rel="preconnect" href="https://fonts.googleapis.com">
            <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
            <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
            <style>
                :root {{
                    --primary: #6366f1;
                    --primary-dark: #4f46e5;
                    --surface-1: #10101d;
                    --surface-2: rgba(30, 30, 46, 0.5);
                    --text-1: #f8fafc;
                    --text-2: #94a3b8;
                }}

                * {{
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }}

                @keyframes gradient {{
                    0% {{ background-position: 0% 50%; }}
                    50% {{ background-position: 100% 50%; }}
                    100% {{ background-position: 0% 50%; }}
                }}

                @keyframes float {{
                    0% {{ transform: translateY(0px); }}
                    50% {{ transform: translateY(-10px); }}
                    100% {{ transform: translateY(0px); }}
                }}

                @keyframes pulse {{
                    0% {{ transform: scale(0.95); opacity: 0.8; }}
                    50% {{ transform: scale(1); opacity: 1; }}
                    100% {{ transform: scale(0.95); opacity: 0.8; }}
                }}

                @keyframes loading {{
                    0% {{ transform: translateX(-100%); }}
                    100% {{ transform: translateX(0); }}
                }}

                @keyframes fadeIn {{
                    from {{ opacity: 0; transform: translateY(10px); }}
                    to {{ opacity: 1; transform: translateY(0); }}
                }}

                body {{
                    font-family: 'Inter', sans-serif;
                    background: linear-gradient(-45deg, #0f172a, #1e1b4b, #172554, #0c4a6e);
                    background-size: 400% 400%;
                    animation: gradient 15s ease infinite;
                    color: var(--text-1);
                    height: 100vh;
                    margin: 0;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    overflow: hidden;
                }}

                .container {{
                    width: 90%;
                    max-width: 520px;
                    position: relative;
                    backdrop-filter: blur(16px);
                    border-radius: 24px;
                    overflow: hidden;
                    animation: fadeIn 0.6s ease-out;
                }}

                .glass {{
                    background: var(--surface-2);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                    box-shadow:
                        0 10px 30px rgba(0, 0, 0, 0.2),
                        inset 0 0 0 1px rgba(255, 255, 255, 0.05);
                    padding: 2.5rem;
                    border-radius: 24px;
                }}

                .blob {{
                    position: absolute;
                    width: 300px;
                    height: 300px;
                    background: linear-gradient(135deg, var(--primary), #8b5cf6);
                    border-radius: 50%;
                    filter: blur(80px);
                    opacity: 0.15;
                    z-index: -1;
                    animation: pulse 8s infinite;
                }}

                .blob-1 {{
                    top: -150px;
                    left: -150px;
                }}

                .blob-2 {{
                    bottom: -150px;
                    right: -150px;
                    animation-delay: 2s;
                }}

                .logo-container {{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-bottom: 1.5rem;
                }}

                .logo {{
                    background: linear-gradient(to right, #c084fc, #4f46e5);
                    border-radius: 14px;
                    padding: 12px;
                    box-shadow: 0 8px 20px rgba(95, 70, 229, 0.25);
                    animation: float 4s ease-in-out infinite;
                }}

                .icon {{
                    font-size: 1.8rem;
                }}

                h1 {{
                    font-weight: 600;
                    font-size: 1.5rem;
                    margin-bottom: 1.5rem;
                    background: linear-gradient(to right, #fff, #cbd5e1);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    text-align: center;
                }}

                .info {{
                    background: rgba(0, 0, 0, 0.15);
                    padding: 1.5rem;
                    border-radius: 16px;
                    margin-bottom: 1.5rem;
                    border: 1px solid rgba(255, 255, 255, 0.05);
                }}

                .destination {{
                    font-size: 0.875rem;
                    color: var(--text-2);
                    margin-bottom: 0.5rem;
                }}

                .url {{
                    font-size: 1rem;
                    font-weight: 500;
                    color: #a5b4fc;
                    word-break: break-all;
                    background: rgba(0, 0, 0, 0.15);
                    padding: 0.75rem 1rem;
                    border-radius: 8px;
                    position: relative;
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                }}

                .url-icon {{
                    color: var(--primary);
                }}

                .security-badge {{
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    background: rgba(22, 163, 74, 0.15);
                    border: 1px solid rgba(22, 163, 74, 0.2);
                    padding: 0.5rem 0.75rem;
                    border-radius: 8px;
                    font-size: 0.75rem;
                    color: #4ade80;
                    margin-top: 0.75rem;
                }}

                .progress-container {{
                    height: 6px;
                    width: 100%;
                    background-color: rgba(255, 255, 255, 0.05);
                    border-radius: 8px;
                    margin: 1.5rem 0;
                    overflow: hidden;
                    position: relative;
                }}

                .progress-bar {{
                    height: 100%;
                    width: 100%;
                    background: linear-gradient(to right, var(--primary), #8b5cf6);
                    border-radius: 8px;
                    transform: translateX(-100%);
                    animation: loading 3s cubic-bezier(0.4, 0, 0.2, 1) forwards;
                }}

                .buttons {{
                    display: flex;
                    gap: 1rem;
                    width: 100%;
                }}

                .button {{
                    flex: 1;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    padding: 0.75rem 1.5rem;
                    border-radius: 12px;
                    font-weight: 500;
                    font-size: 0.9rem;
                    transition: all 0.2s ease;
                    cursor: pointer;
                    border: none;
                    text-decoration: none;
                }}

                .button-primary {{
                    background: linear-gradient(135deg, var(--primary), var(--primary-dark));
                    color: white;
                    box-shadow: 0 8px 16px rgba(99, 102, 241, 0.25);
                }}

                .button-primary:hover {{
                    transform: translateY(-2px);
                    box-shadow: 0 12px 20px rgba(99, 102, 241, 0.35);
                }}

                .button-outline {{
                    background: rgba(255, 255, 255, 0.05);
                    color: var(--text-1);
                    border: 1px solid rgba(255, 255, 255, 0.1);
                }}

                .button-outline:hover {{
                    background: rgba(255, 255, 255, 0.1);
                }}

                .countdown {{
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    gap: 0.5rem;
                    margin-top: 1.5rem;
                    color: var(--text-2);
                    font-size: 0.875rem;
                }}

                .countdown-number {{
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    min-width: 1.5rem;
                    height: 1.5rem;
                    background: rgba(0, 0, 0, 0.2);
                    border-radius: 4px;
                    font-variant-numeric: tabular-nums;
                    font-weight: 500;
                    color: var(--text-1);
                }}

                @media (max-width: 480px) {{
                    .glass {{
                        padding: 1.5rem;
                    }}

                    .buttons {{
                        flex-direction: column;
                    }}
                }}
            </style>
            <script>
                // Countdown script
                document.addEventListener('DOMContentLoaded', function() {{
                    let count = 2;
                    const countdownEl = document.getElementById('countdown-number');

                    const interval = setInterval(() => {{
                        count--;
                        countdownEl.textContent = count;

                        if (count <= 0) {{
                            clearInterval(interval);
                        }}
                    }}, 1000);

                    // Log tracking parameters for debugging
                    console.log('AdMesh tracking parameters:', {{
                        redirectUrl: window.location.href,
                        destination: '{domain}'
                    }});
                }});
            </script>
        </head>
        <body>
            <div class="container">
                <div class="blob blob-1"></div>
                <div class="blob blob-2"></div>
                <div class="glass">
                    <div class="logo-container">
                        <div class="logo">
                            <div class="icon">⚡</div>
                        </div>
                    </div>
                    <h1>You're being redirected</h1>

                    <div class="info">
                        <div class="destination">Destination website:</div>
                        <div class="url">
                            <span class="url-icon">🔗</span>
                            <span>{domain}</span>
                        </div>
                        <div class="security-badge">
                            <span>✓</span>
                            <span>Secure redirect verified</span>
                        </div>
                    </div>

                    <div class="progress-container">
                        <div class="progress-bar"></div>
                    </div>

                    <div class="buttons">
                        <a href="{redirect_url}" class="button button-primary">
                            Continue now
                        </a>
                        <button onclick="window.close()" class="button button-outline">
                            Cancel
                        </button>
                    </div>
                    <div class="tracking-info" style="font-size: 0.7rem; margin-top: 0.5rem; text-align: center; color: var(--text-2);">
                        <span>Tracking enabled for analytics</span>
                    </div>

                    <div class="countdown">
                        Redirecting in <span id="countdown-number" class="countdown-number">3</span> seconds
                    </div>
                </div>
            </div>
        </body>
        </html>
        """
        return HTMLResponse(content=html)

    except Exception as e:
        logger.exception(f"🔥 Redirect failed for ad_id {ad_id}")
        raise HTTPException(status_code=500, detail="Redirect error")
