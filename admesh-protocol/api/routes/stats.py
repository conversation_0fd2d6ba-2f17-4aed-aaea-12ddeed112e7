from fastapi import APIRouter, HTTPException, Query
from firebase.config import get_db
from typing import Optional
from datetime import datetime, timedelta

router = APIRouter()
db = get_db()

@router.get("/offers/stats/{offer_id}")
async def get_offer_stats(offer_id: str):
    try:
        offer_ref = db.collection("offers").document(offer_id).get()
        if not offer_ref.exists:
            raise HTTPException(status_code=404, detail="Offer not found")

        offer = offer_ref.to_dict()

        # Extract click counts - use production clicks only for CTR
        click_data = offer.get("click_count", 0)
        if isinstance(click_data, dict):
            clicks_total = click_data.get("total", 0)
            clicks_production = click_data.get("production", 0)
            clicks_test = click_data.get("test", 0)
        else:
            # Legacy format - assume all are production
            clicks_total = clicks_production = click_data
            clicks_test = 0

        # Extract conversion counts - use production conversions only for CTR
        conversion_data = offer.get("conversion_count", 0)
        if isinstance(conversion_data, dict):
            conversions_total = conversion_data.get("total", 0)
            conversions_production = conversion_data.get("production", 0)
            conversions_test = conversion_data.get("test", 0)
        else:
            # Legacy format - assume all are production
            conversions_total = conversions_production = conversion_data
            conversions_test = 0

        # Extract total spent
        total_spent_data = offer.get("total_spent", 0.0)
        if isinstance(total_spent_data, dict):
            total_spent_production = total_spent_data.get("production", 0.0)
            total_spent_test = total_spent_data.get("test", 0.0)
            total_spent_all = total_spent_production + total_spent_test
        else:
            # Legacy format - assume all is production
            total_spent_production = total_spent_all = total_spent_data
            total_spent_test = 0.0

        payout_model = offer.get("payout", {}).get("model", "CPA")

        # Calculate CTR using only production clicks and conversions (excluding test clicks and conversions)
        ctr = (conversions_production / clicks_production * 100.0) if clicks_production > 0 else 0.0

        return {
            "offer_id": offer_id,
            "click_count": {
                "total": clicks_total,
                "production": clicks_production,
                "test": clicks_test
            },
            "conversion_count": {
                "total": conversions_total,
                "production": conversions_production,
                "test": conversions_test
            },
            "ctr": round(ctr, 2),
            "total_spent": {
                "production": total_spent_production,
                "test": total_spent_test,
                "all": total_spent_all
            },
            "payout_model": payout_model
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/offers/stats/{offer_id}/detailed")
async def get_detailed_offer_stats(
    offer_id: str,
    time_range: Optional[str] = Query("all", description="Time range for stats: 7d, 30d, or all"),
    conversion_type: Optional[str] = Query("all", description="Filter by conversion type: test, production, or all")
):
    try:
        # Get offer details
        offer_ref = db.collection("offers").document(offer_id).get()
        if not offer_ref.exists:
            raise HTTPException(status_code=404, detail="Offer not found")

        offer = offer_ref.to_dict()

        # Get brand wallet information
        brand_id = offer.get("brand_id")
        wallet_data = None
        if brand_id:
            wallet_ref = db.collection("wallets").document(brand_id)
            wallet_doc = wallet_ref.get()
            if wallet_doc.exists:
                wallet_data = wallet_doc.to_dict()
        print (f"Wallet data: {wallet_data}")

        # Basic offer stats
        budget = offer.get("offer_total_budget_allocated", 0.0)
        total_spent_data = offer.get("total_spent", 0.0)

        # Handle different formats of total_spent
        if isinstance(total_spent_data, dict):
            # Ensure we have non-negative values for spent amounts
            total_spent_all = max(0, total_spent_data.get("all", 0.0))
            total_spent_production = max(0, total_spent_data.get("production", 0.0))
            total_spent_test = max(0, total_spent_data.get("test", 0.0))
        else:
            # Legacy format - assume all is production
            total_spent_all = max(0, total_spent_data)
            total_spent_production = total_spent_all
            total_spent_test = 0.0

        payout = offer.get("payout", {})

        # Get conversions with filters
        conversions_query = db.collection("conversions").where("offer_id", "==", offer_id)

        # Apply time range filter if specified
        if time_range != "all":
            days = int(time_range.replace("d", ""))
            cutoff_date = datetime.now() - timedelta(days=days)
            conversions_query = conversions_query.where("timestamp", ">=", cutoff_date)

        # Get conversion documents
        conversions_docs = conversions_query.stream()

        # Process conversions
        test_conversions = []
        production_conversions = []

        for doc in conversions_docs:
            conversion_data = doc.to_dict()
            conversion_item = {
                "id": doc.id,
                "timestamp": conversion_data.get("timestamp"),
                "value": conversion_data.get("conversion_value", 0.0),
                "currency": conversion_data.get("currency", "USD"),
                "source": conversion_data.get("source", "unknown"),
                "agent_id": conversion_data.get("agent_id"),
                "user_id": conversion_data.get("user_id"),
                "status": conversion_data.get("status", "pending")
            }

            # Categorize by test or production
            # Check both is_test flag and source field for test indicators
            source = conversion_data.get("source", "").lower()
            if conversion_data.get("is_test", False) or "test" in source:
                test_conversions.append(conversion_item)
            else:
                production_conversions.append(conversion_item)

        # Get clicks
        clicks_query = db.collection("clicks").where("ad_id", "==", offer_id)

        # Filter out unclaimed clicks
        clicks_query = clicks_query.where("is_unclaimed_click", "==", False)

        # Apply time range filter for clicks too
        if time_range != "all":
            clicks_query = clicks_query.where("timestamp", ">=", cutoff_date)

        # Get all clicks and separate test from production
        all_clicks = list(clicks_query.stream())
        test_clicks = []
        production_clicks = []

        for click_doc in all_clicks:
            click_data = click_doc.to_dict()
            # Check both is_test flag and source field for test indicators
            source = click_data.get("source", "").lower()
            if click_data.get("is_test", False) or "test" in source:
                test_clicks.append(click_data)
            else:
                production_clicks.append(click_data)

        # Count clicks
        test_click_count = len(test_clicks)
        production_click_count = len(production_clicks)
        total_click_count = test_click_count + production_click_count

        # Calculate metrics
        test_conversion_count = len(test_conversions)
        production_conversion_count = len(production_conversions)
        total_conversion_count = test_conversion_count + production_conversion_count

        # Calculate spent amounts separately for production and test conversions
        production_spent = sum(conv.get("value", 0.0) for conv in production_conversions)
        test_spent = sum(conv.get("value", 0.0) for conv in test_conversions)

        # Log the values for debugging
        print(f"DEBUG - Offer {offer_id}: offer_total_budget_allocated={budget}, Production Spent={production_spent}, Test Spent={test_spent}")
        print(f"DEBUG - Stored values: Total={total_spent_all}, Production={total_spent_production}, Test={total_spent_test}")
        print(f"DEBUG - Clicks: Total={total_click_count}, Production={production_click_count}, Test={test_click_count} (filtered out unclaimed clicks)")

        # Log the new offer-specific budget tracking fields
        print(f"DEBUG - New budget fields: offer_total_budget_available={offer.get('offer_total_budget_available', 0.0)}, " +
              f"offer_total_budget_spent={offer.get('offer_total_budget_spent', 0.0)}, " +
              f"offer_total_promo_spent={offer.get('offer_total_promo_spent', 0.0)}, " +
              f"offer_total_promo_available={offer.get('offer_total_promo_available', 0.0)}, " +
              f"offer_intial_promo_balance={offer.get('offer_intial_promo_balance', 0.0)}, " +
              f"promo_conversions_left={offer.get('promo_conversions_left', 0)}, " +
              f"promo_applied={offer.get('promo_applied', False)}")

        # Filter conversions based on type parameter
        filtered_conversions = []
        if conversion_type == "test":
            filtered_conversions = test_conversions
        elif conversion_type == "production":
            filtered_conversions = production_conversions
        else:  # "all"
            filtered_conversions = test_conversions + production_conversions

        # Calculate CTR using only production conversions and production clicks
        ctr = (production_conversion_count / production_click_count * 100.0) if production_click_count > 0 else 0.0

        # Get product name if product_id exists
        product_name = None
        if offer.get("product_id"):
            product_ref = db.collection("products").document(offer.get("product_id")).get()
            if product_ref.exists:
                product_name = product_ref.to_dict().get("title")

        # Return detailed stats with all offer information
        return {
            "offer_id": offer_id,
            "title": offer.get("title", ""),
            "description": offer.get("description", ""),
            "url": offer.get("url", ""),
            "offer_total_budget_allocated": budget,
            "offer_budget_left": max(0, budget - production_spent),  # Only production counts against budget
            "calculated_budget_left": max(0, budget - production_spent),  # Explicit calculation for frontend verification
            "total_spent": {
                "production": production_spent,
                "test": max(0, sum(conv.get("value", 0.0) for conv in test_conversions)),
                "all": max(0, total_spent_all)  # Keep original for backward compatibility but ensure non-negative
            },
            "stored_total_spent": {
                "production": total_spent_production,
                "test": total_spent_test,
                "all": total_spent_all
            },
            "click_count": {
                "total": total_click_count,
                "test": test_click_count,
                "production": production_click_count
            },
            "view_count": offer.get("view_count", 0),  # Legacy view count
            "offer_views": offer.get("offer_views", {
                "total": 0,
                "test": 0,
                "production": 0
            }),  # New structured view tracking
            "conversion_count": {
                "total": total_conversion_count,
                "test": test_conversion_count,
                "production": production_conversion_count
            },
            "ctr": round(ctr, 2),
            "payout": payout,
            "conversions": filtered_conversions,
            "active": offer.get("active", False),
            "created_at": offer.get("created_at"),
            "updated_at": offer.get("updated_at"),
            "valid_until": offer.get("valid_until"),
            "brand_id": offer.get("brand_id"),
            "product_id": offer.get("product_id"),
            "product_name": product_name,
            "goal": offer.get("goal"),
            "reward_note": offer.get("reward_note"),
            "suggestion_reason": offer.get("suggestion_reason"),
            "categories": offer.get("categories", []),
            "keywords": offer.get("keywords", []),
            "trust_score": offer.get("trust_score"),  # Legacy trust score
            "offer_trust_score": offer.get("offer_trust_score"),  # New trust score
            "tracking": offer.get("tracking"),

            # New offer-specific budget tracking fields
            "offer_total_budget_available": offer.get("offer_total_budget_available", 0.0),
            "offer_total_budget_spent": offer.get("offer_total_budget_spent", 0.0),
            "offer_total_promo_spent": offer.get("offer_total_promo_spent", 0.0),
            "offer_total_promo_available": offer.get("offer_total_promo_available", 0.0),
            "offer_intial_promo_balance": offer.get("offer_intial_promo_balance", 0.0),
            "promo_conversions_left": offer.get("promo_conversions_left", 0),
            "promo_applied": offer.get("promo_applied", False),

            # Wallet information
            "wallet": wallet_data if wallet_data else {
                "total_available_balance": 0.0,
                "total_promo_available_balance": 0.0,
                "total_promo_balance_spent": 0.0,
                "total_balance_spent": 0.0,
                "total_budget_allocated": 0.0
            },
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
