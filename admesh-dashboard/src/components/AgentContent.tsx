"use client";

import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import MobileNav from "@/components/MobileNav";
import { useState, useEffect } from "react";
import { useInView } from "react-intersection-observer";
import { useAuth } from "@/hooks/use-auth";
import { useRouter } from "next/navigation";
import {
  Code,
  Cpu,
  DollarSign,
  Package,
  Users,
  BarChart,
  ChevronDown,
  Zap,
} from "lucide-react";

type Role = "user" | "brand" | "agent" | null;

interface AgentContentProps {
  onRoleChange: (role: Role) => void;
}

export default function AgentContent({ /* onRoleChange */ }: AgentContentProps) {
  const [, setScrollProgress] = useState(0);
  const [activeSection, setActiveSection] = useState("hero");
  const { user } = useAuth();
  const router = useRouter();

  // Track scroll position for animations and nav highlighting
  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.scrollY;
      const docHeight = document.body.offsetHeight - window.innerHeight;
      const scrollPercent = (scrollTop / docHeight) * 100;
      setScrollProgress(scrollPercent);

      // Update active section based on scroll position
      const sections = ["hero", "journey", "benefits", "pioneer-program"];
      sections.forEach((section) => {
        const element = document.getElementById(section);
        if (element) {
          const rect = element.getBoundingClientRect();
          if (
            rect.top <= window.innerHeight / 2 &&
            rect.bottom >= window.innerHeight / 2
          ) {
            setActiveSection(section);
          }
        }
      });
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // InView hooks for each section
  const { ref: heroRef, inView: heroInView } = useInView({ triggerOnce: true });
  const { ref: journeyRef, inView: journeyInView } = useInView({
    triggerOnce: true,
  });
  const { ref: benefitsRef, inView: benefitsInView } = useInView({
    triggerOnce: true,
  });

  const scrollToSection = (sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: "smooth" });
    }
  };



  const benefits = [
    {
      icon: <Package className="w-10 h-10 text-primary" />,
      title: "No Inventory Required",
      description:
        "Access thousands of brand offers without managing partnerships.",
    },
    {
      icon: <Users className="w-10 h-10 text-primary" />,
      title: "Better User Outcomes",
      description:
        "Recommend value-aligned products that increase trust and engagement.",
    },
    {
      icon: <BarChart className="w-10 h-10 text-primary" />,
      title: "Clear Earning Logic",
      description:
        "View clicks, conversions, and payouts in real-time — with full transparency.",
    },
  ];

  return (
    <div className="w-full">
      {/* Mobile-friendly Navigation */}
      <MobileNav
        items={[
          { label: "Home", sectionId: "hero" },
          { label: "How It Works", sectionId: "journey" },
          { label: "Benefits", sectionId: "benefits" },
          { label: "Pioneer Program", sectionId: "pioneer-program" },
        ]}
        activeSection={activeSection}
        scrollToSection={scrollToSection}
        onGetStarted={() => router.push('/get-started?role=agent')}
        themeColor="black"
        user={user}
      />

      {/* Hero Section */}
      <section
        id="hero"
        ref={heroRef}
        className="w-full min-h-screen flex items-center justify-center bg-background pt-20 pb-16 px-4"
      >
        <div className="max-w-6xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="text-center lg:text-left">
            <motion.span
              className="inline-block mb-3 px-3 py-1 bg-muted text-foreground rounded-full text-sm font-medium"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: heroInView ? 1 : 0, y: heroInView ? 0 : 20 }}
              transition={{ duration: 0.3 }}
            >
              For AI Agents
            </motion.span>

            <motion.h1
              className="text-4xl sm:text-5xl md:text-6xl font-extrabold tracking-tight leading-tight mb-6 text-foreground"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: heroInView ? 1 : 0, y: heroInView ? 0 : 20 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              Monetize Your AI Agent Through the AdMesh Protocol
            </motion.h1>

            <motion.p
              className="text-lg sm:text-xl text-muted-foreground max-w-xl mx-auto lg:mx-0 mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: heroInView ? 1 : 0, y: heroInView ? 0 : 20 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              Integrate in minutes. Recommend verified offers. Earn when users
              take real action.
            </motion.p>

            <motion.div
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: heroInView ? 1 : 0, y: heroInView ? 0 : 20 }}
              transition={{ duration: 0.5, delay: 0.3 }}
            >
              <Button
                size="lg"
                className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium shadow-md hover:shadow-lg transition-all"
                onClick={() => router.push('/get-started?role=agent')}
              >
                Join Waitlist
              </Button>

              <Button
                variant="outline"
                size="lg"
                className="border-border hover:bg-muted"
                onClick={() => scrollToSection("journey")}
              >
                Learn More
              </Button>
            </motion.div>
          </div>

          <motion.div
            className="relative rounded-xl p-1 bg-primary shadow-xl"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{
              opacity: heroInView ? 1 : 0,
              scale: heroInView ? 1 : 0.95,
            }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <div className="bg-card rounded-lg p-6 h-full">
              <div className="mb-6 flex justify-between items-center">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 rounded-full bg-primary flex items-center justify-center">
                    <Cpu className="w-5 h-5 text-primary-foreground" />
                  </div>
                  <div>
                    <h3 className="font-bold text-foreground">Agent Dashboard</h3>
                    <p className="text-sm text-muted-foreground">MVP Preview</p>
                  </div>
                </div>
                <div className="bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-400 px-3 py-1 rounded-full text-xs font-medium">
                  Early Access
                </div>
              </div>

              <div className="space-y-6">
                {/* Earnings */}
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span className="text-muted-foreground">Estimated Earnings</span>
                    <span className="font-medium text-foreground">$3693.00</span>
                  </div>
                </div>

                {/* Key Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-muted rounded-lg p-5 hover:shadow transition">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-foreground">
                        Intent Conversion Rate
                      </span>
                      <BarChart className="w-4 h-4 text-primary" />
                    </div>
                    <p className="text-3xl font-bold text-foreground">23.8%</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Across 213 matched queries
                    </p>
                  </div>
                  <div className="bg-muted rounded-lg p-5 hover:shadow transition">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-medium text-foreground">
                        Avg. Conversion Payout
                      </span>
                      <DollarSign className="w-4 h-4 text-primary" />
                    </div>
                    <p className="text-3xl font-bold text-foreground">$9.99</p>
                    <p className="text-xs text-muted-foreground mt-1">
                      Per verified action
                    </p>
                  </div>
                </div>

                {/* Top Queries */}
                <div className="pt-5 pb-3 border-t border-border">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="text-sm font-medium text-foreground">
                      Top Performing Intents
                    </h4>
                    <span className="text-xs text-muted-foreground hover:text-foreground cursor-pointer">
                      Join Waitlist
                    </span>
                  </div>
                  <div className="space-y-3">
                    {[
                      { query: "Best CRM tools", ctr: 76 },
                      { query: "AI writing assistant", ctr: 61 },
                      { query: "Startup launch kit", ctr: 48 },
                    ].map((item, i) => (
                      <div key={i} className="flex items-center gap-3">
                        <div
                          className={`w-9 h-9 ${
                            i === 0
                              ? "bg-primary text-primary-foreground"
                              : "bg-muted text-foreground"
                          } rounded-md flex items-center justify-center font-bold`}
                        >
                          {i + 1}
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between text-sm mb-1.5">
                            <span className="font-medium text-foreground">{item.query}</span>
                            <span className="font-bold text-foreground">{item.ctr}% CTR</span>
                          </div>
                          <div className="w-full bg-muted rounded-full h-2">
                            <div
                              className={`${
                                i === 0 ? "bg-primary" : "bg-primary/60"
                              } h-2 rounded-full`}
                              style={{ width: `${item.ctr}%` }}
                            ></div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: heroInView ? 1 : 0, y: heroInView ? 0 : 20 }}
          transition={{ duration: 0.5, delay: 0.8 }}
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-center hidden md:block"
        >
          <button
            onClick={() => scrollToSection("journey")}
            className="flex flex-col items-center text-muted-foreground hover:text-foreground transition-colors"
          >
            <span className="text-sm mb-2">Discover More</span>
            <ChevronDown className="w-5 h-5 animate-bounce" />
          </button>
        </motion.div>
      </section>
{/* Journey Section */}
<section id="journey" ref={journeyRef} className="w-full py-20 bg-background">
  <div className="max-w-6xl mx-auto px-4">
    <motion.div
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: journeyInView ? 1 : 0, y: journeyInView ? 0 : 30 }}
      transition={{ duration: 0.6 }}
      className="text-center mb-16"
    >
      <span className="text-sm font-medium text-muted-foreground block mb-3">
        HOW IT WORKS
      </span>
      <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
        Integrate, Recommend, Track
      </h2>
      <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
        AdMesh gives AI agents a clean path to plug into verified brand offers, deliver recommendations, and track every user action.
      </p>
    </motion.div>

    <div className="relative">
      {/* Connection Line */}
      <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-border transform -translate-y-1/2 z-0"></div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-12 relative z-10">
        {[
          {
            icon: <Code className="w-10 h-10 text-primary" />,
            title: "Connect to AdMesh",
            description: "Use our SDK or REST API to authenticate your agent and fetch live recommendations."
          },
          {
            icon: <Zap className="w-10 h-10 text-primary" />,
            title: "Respond with Offers",
            description: "Get structured, intent-aligned offers and display them in chat, UI, or voice output."
          },
          {
            icon: <BarChart className="w-10 h-10 text-primary" />,
            title: "Track & Earn",
            description: "Track every click and conversion. Earn based on real value delivered to users."
          }
        ].map((step, index) => (
          <motion.div
            key={index}
            className="bg-card rounded-xl p-8 shadow-lg border border-border relative"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: journeyInView ? 1 : 0, y: journeyInView ? 0 : 20 }}
            transition={{ duration: 0.5, delay: 0.15 * index }}
          >
            <div className="w-14 h-14 rounded-full bg-muted flex items-center justify-center mb-6">
              {step.icon}
            </div>
            <span className="absolute top-8 right-8 text-5xl font-bold text-muted select-none">
              {index + 1}
            </span>
            <h3 className="text-xl font-semibold mb-3 text-foreground">{step.title}</h3>
            <p className="text-muted-foreground">{step.description}</p>
          </motion.div>
        ))}
      </div>
    </div>

    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: journeyInView ? 1 : 0, y: journeyInView ? 0 : 20 }}
      transition={{ duration: 0.5, delay: 0.6 }}
      className="text-center mt-12"
    >
      <Button
        size="lg"
        className="bg-primary hover:bg-primary/90 text-primary-foreground"
        onClick={() => router.push('/get-started?role=agent')}
      >
        Join Waitlist
      </Button>
    </motion.div>
  </div>
</section>

      {/* Benefits Section */}
      <section
        id="benefits"
        ref={benefitsRef}
        className="w-full py-20 bg-muted"
      >
        <div className="max-w-6xl mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{
              opacity: benefitsInView ? 1 : 0,
              y: benefitsInView ? 0 : 30,
            }}
            transition={{ duration: 0.6 }}
            className="text-center mb-16"
          >
            <span className="text-sm font-medium text-muted-foreground block mb-3">
              WHY CHOOSE ADMESH
            </span>
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Why Agents Choose AdMesh
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Discover why leading AI agents are integrating with the AdMesh
              protocol.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                className="bg-card rounded-xl p-8 shadow-lg border border-border hover:shadow-xl transition-shadow"
                initial={{ opacity: 0, y: 20 }}
                animate={{
                  opacity: benefitsInView ? 1 : 0,
                  y: benefitsInView ? 0 : 20,
                }}
                transition={{ duration: 0.5, delay: 0.15 * index }}
              >
                <div className="w-14 h-14 rounded-full bg-muted flex items-center justify-center mb-6">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-semibold mb-3 text-foreground">{benefit.title}</h3>
                <p className="text-muted-foreground">{benefit.description}</p>
              </motion.div>
            ))}
          </div>


          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{
              opacity: benefitsInView ? 1 : 0,
              y: benefitsInView ? 0 : 20,
            }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="text-center mt-12"
          >
            <Button
              size="lg"
              className="bg-primary hover:bg-primary/90 text-primary-foreground"
              onClick={() => router.push('/get-started?role=agent')}
            >
              Join Waitlist
            </Button>
          </motion.div>
        </div>
      </section>



      {/* CTA Section */}
      <section id="pioneer-program" className="w-full py-20 bg-primary text-primary-foreground">
        <div className="max-w-6xl mx-auto px-4 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: benefitsInView ? 1 : 0, y: benefitsInView ? 0 : 20 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-primary-foreground">
              Ready to Monetize Your AI Agent?
            </h2>
            <p className="text-xl text-primary-foreground/80 mb-8 max-w-2xl mx-auto">
              Join the AdMesh protocol today and start earning from relevant
              recommendations.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                size="lg"
                className="bg-background text-foreground hover:bg-background/90"
                onClick={() => router.push('/get-started?role=agent')}
              >
                Join Waitlist
              </Button>


            </div>


          </motion.div>
        </div>
      </section>

    </div>
  );
}
