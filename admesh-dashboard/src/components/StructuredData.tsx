"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";

type StructuredDataProps = {
  type: "Organization" | "WebSite" | "FAQPage" | "Product" | "Article" | "WebPage" | string;
  data: Record<string, unknown>;
};

export default function StructuredData({ type, data }: StructuredDataProps) {
  const [structuredData, setStructuredData] = useState<Record<string, unknown>>({});

  useEffect(() => {
    // Combine the type and data into a structured data object
    const structuredDataObj = {
      "@context": "https://schema.org",
      "@type": type,
      ...data,
    };

    setStructuredData(structuredDataObj);
  }, [type, data]);

  // Only render on the client side
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

// Component for adding WebPage structured data to main pages
export function MainPageStructuredData() {
  const pathname = usePathname();
  const baseUrl = "https://www.useadmesh.com";

  // Default data
  const defaultData = {
    "@type": "WebPage",
    url: `${baseUrl}${pathname}`,
    isPartOf: {
      "@type": "WebSite",
      name: "AdMesh",
      url: baseUrl
    }
  };

  // Page-specific data
  let pageData = {};

  if (pathname === "/" || pathname === "/brands") {
    pageData = {
      name: "For Brands",
      description: "Connect with high-intent users looking for your products. AdMesh helps brands reach users at the perfect moment when they're actively searching for solutions.",
      mainEntity: {
        "@type": "Service",
        name: "AdMesh for Brands",
        offers: {
          "@type": "Offer",
          url: `${baseUrl}/brands#pricing`
        },
        description: "Connect with high-intent users looking for your products"
      },
      speakable: {
        "@type": "SpeakableSpecification",
        cssSelector: ["h1", ".hero-text"]
      }
    };
  } else if (pathname === "/agents") {
    pageData = {
      name: "For Agents",
      description: "Join the AdMesh Agent Pioneer Program. Earn rewards by helping users discover the perfect tools and products for their needs.",
      mainEntity: {
        "@type": "Service",
        name: "AdMesh for Agents",
        description: "Join the AdMesh Agent Pioneer Program"
      },
      speakable: {
        "@type": "SpeakableSpecification",
        cssSelector: ["h1", ".hero-text"]
      }
    };
  } else if (pathname === "/users") {
    pageData = {
      name: "For Users",
      description: "Discover AI tools and earn rewards with AdMesh.",
      mainEntity: {
        "@type": "Service",
        name: "AdMesh for Users",
        description: "Discover AI tools and earn rewards"
      },
      speakable: {
        "@type": "SpeakableSpecification",
        cssSelector: ["h1", ".hero-text"]
      }
    };
  }

  const structuredData = {
    ...defaultData,
    ...pageData
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
    />
  );
}

